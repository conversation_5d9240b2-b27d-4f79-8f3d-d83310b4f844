<project version="3.3.0" format="2" commercial="Aspen SKUA V14.2.1">
	<plugins bundle="skua_reservoir_modeling">
		<plugin name="Gocad" version="3.3.1" build-identifier="20240429-0439 (id: V14.2|1664666|P4V14.21664666|20240429-0439)" option="viewer|Base|Surface|Volume|GenDataAnalysis|Geostat|BasicVelModeling|TimeDepth"/>
		<plugin name="Filters" version="3.3.1" build-identifier="20240429-0439 (id: V14.2|1664666|P4V14.21664666|20240429-0439)" option=""/>
		<plugin name="PluginFoundations" version="3.3.1" build-identifier="20240429-0439 (id: V14.2|1664666|P4V14.21664666|20240429-0439)" option="AdvancedPropagator|PFFilters|SeismicAttributes|SeismicFaciesClassification"/>
		<plugin name="WorkflowFoundations" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="cmw"/>
		<plugin name="Tessellations" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="GraphableObjectViewer" version="3.3.1" build-identifier="20240429-0439 (id: V14.2|1664666|P4V14.21664666|20240429-0439)" option="gov"/>
		<plugin name="InGrid" version="3.3.1" build-identifier="20240429-0439 (id: V14.2|1664666|P4V14.21664666|20240429-0439)" option="InGridViewer|InGridStrati|InGridStruct|InGridSGrid|SKUAHybridGrid|SKUAUnstructFSG25D"/>
		<plugin name="XMLWorkflow" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="Views2D" version="3.3.1" build-identifier="20240429-0439 (id: V14.2|1664666|P4V14.21664666|20240429-0439)" option="Correlation|Views2dMapViewer|Views2dXSectionViewer|Views2dLogViewer"/>
		<plugin name="InGridUncertainty" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="InGridUncertainty"/>
		<plugin name="Jacta" version="3.3.1" build-identifier="20240429-0439 (id: V14.2|1664666|P4V14.21664666|20240429-0439)" option=""/>
		<plugin name="PropertyModeling" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="Geostat|PMWCategorical|Geostat|PMWContinuous|PMWPostProcessing"/>
		<plugin name="UpscalingTools" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="UpscalingBasic|UpscalingBasic|UpscalingDynamic"/>
		<plugin name="ReservoirProduction" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="ProductionDataAnalysis" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="pda_basic|pda_scenario|pda_gui"/>
		<plugin name="VolumeExplorer" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="VolumeExplorer"/>
		<plugin name="Kine3D1" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="Kine3D1"/>
		<plugin name="KivSimEiBase" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="JactaInGrid" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="Kine3D3" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="Kine3D3"/>
		<plugin name="StratigraphicModeling" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="StratiModel"/>
		<plugin name="FilterIsatis" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="JactaPM" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="FluidSaturationWorkflow" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="ReScaling" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="RescalingWF|RescalingUtils|Upscaler|Downscaler|LGR"/>
		<plugin name="StructuralModeling" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="StructFramework|StructLayer"/>
		<plugin name="TessellationsWorkflow" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="TessellationsWorkflow"/>
		<plugin name="ProductionDataUncertainty" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="InterpreterAssistant" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="SeismicInterpretation|SeismicPaleoRestoration"/>
		<plugin name="FracMV" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="Stereonet" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="Stereonet"/>
		<plugin name="IDP" version="3.3.1" build-identifier="20240429-0439 (id: V14.2|1664666|P4V14.21664666|20240429-0439)" option="WellPlanning|PlatformPlanning|DrillingUncertainty|AntiCollision|SideTrackPlanning"/>
		<plugin name="StochasticRefinements" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="Geostat|StochasticRefinements"/>
		<plugin name="JactaFrontSim" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="Go4space" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="Go4space"/>
		<plugin name="Kine3D2" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="Kine3D12Surface"/>
		<plugin name="MultipointSimulation" version="3.3.1" build-identifier="20240429-0439 (id: V14.2|1664666|P4V14.21664666|20240429-0439)" option="MultipointSimulationFunction1"/>
		<plugin name="PluriGaussianSimulation" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="Jacta3dsl" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="ScriptAPI" version="3.3.1" build-identifier="20240429-0439 (id: V14.2|1664666|P4V14.21664666|20240429-0439)" option=""/>
		<plugin name="DataAnalysisWorkflow" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option="DAWBlocking"/>
		<plugin name="AbaqusConnector" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="Boolx" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
		<plugin name="Alea" version="3.3.0" build-identifier="************* (id: V14.2|1622787|P4V14.21622787|*************)" option=""/>
	</plugins>
	<objects>
		<object>
			<name><![CDATA[style_catalog]]></name>
		</object>
		<object>
			<name><![CDATA[style_template_mgr]]></name>
		</object>
		<object>
			<name><![CDATA[mig01]]></name>
		</object>
		<object>
			<name><![CDATA[mig01]]></name>
		</object>
		<object>
			<name><![CDATA[Favorites]]></name>
		</object>
		<object>
			<name><![CDATA[strati_context]]></name>
		</object>
		<object>
			<name><![CDATA[2DMappingFromInterpretation]]></name>
		</object>
		<object>
			<name><![CDATA[Apply_Structural_Window_Filtering]]></name>
		</object>
		<object>
			<name><![CDATA[AssignRandomColors]]></name>
		</object>
		<object>
			<name><![CDATA[Auto_Guess_Variogram]]></name>
		</object>
		<object>
			<name><![CDATA[Auto_Property_Modeling]]></name>
		</object>
		<object>
			<name><![CDATA[Auto_Structural_Modeling]]></name>
		</object>
		<object>
			<name><![CDATA[ComputeCurvesDipAndAzimuth]]></name>
		</object>
		<object>
			<name><![CDATA[ComputeDistanceToCurves]]></name>
		</object>
		<object>
			<name><![CDATA[ComputeDistanceToWells]]></name>
		</object>
		<object>
			<name><![CDATA[ComputeGeologicalCurvature]]></name>
		</object>
		<object>
			<name><![CDATA[Copy_Objects]]></name>
		</object>
		<object>
			<name><![CDATA[CreateIndexPropertiesOnGrid]]></name>
		</object>
		<object>
			<name><![CDATA[Create_Curves_from_Built_in_Shapes]]></name>
		</object>
		<object>
			<name><![CDATA[Create_Objects_from_Well_Horizontal_Path]]></name>
		</object>
		<object>
			<name><![CDATA[Create_Probe_Around_Objects]]></name>
		</object>
		<object>
			<name><![CDATA[Create_Surfaces_from_Built_in_Shapes]]></name>
		</object>
		<object>
			<name><![CDATA[EZModelBuildingFromSurfacesAndWells]]></name>
		</object>
		<object>
			<name><![CDATA[ExamplesDataSelectionandUI]]></name>
		</object>
		<object>
			<name><![CDATA[ExportFlowGridsFromUncertaintyRealizations]]></name>
		</object>
		<object>
			<name><![CDATA[ExtractMapPropertiesAtWellLocations]]></name>
		</object>
		<object>
			<name><![CDATA[Gridding_Interpretations]]></name>
		</object>
		<object>
			<name><![CDATA[Horizontal_Wells_Toe_Analysis]]></name>
		</object>
		<object>
			<name><![CDATA[K3D_Reorient_horizons_with_cell_ages]]></name>
		</object>
		<object>
			<name><![CDATA[MatrixViewForObjectsAndProperties]]></name>
		</object>
		<object>
			<name><![CDATA[QCMeshQuality]]></name>
		</object>
		<object>
			<name><![CDATA[Rename_Objects]]></name>
		</object>
		<object>
			<name><![CDATA[RescaleProperty]]></name>
		</object>
		<object>
			<name><![CDATA[SubstitutePropertyValues]]></name>
		</object>
		<object>
			<name><![CDATA[TransferRegionsBetweenGrids]]></name>
		</object>
		<object>
			<name><![CDATA[UpdateSKUAModelWithTomoPencils]]></name>
		</object>
		<object>
			<name><![CDATA[WellsClassification]]></name>
		</object>
		<object>
			<name><![CDATA[application_objects]]></name>
		</object>
		<object>
			<name><![CDATA[geobaselib]]></name>
		</object>
		<object>
			<name><![CDATA[geologic_features]]></name>
		</object>
		<object>
			<name><![CDATA[viewer_entity_mgr]]></name>
		</object>
		<object>
			<name><![CDATA[entity_view_settings_catalog]]></name>
		</object>
		<object>
			<name><![CDATA[colormap_entity_mgr]]></name>
		</object>
		<object>
			<name><![CDATA[fluid_component_mgr]]></name>
		</object>
		<object>
			<name><![CDATA[viewer_view_settings_mgr]]></name>
		</object>
		<object>
			<name><![CDATA[gpg_well_list_settings_catalog]]></name>
		</object>
		<object>
			<name><![CDATA[UniqueIdMapping]]></name>
		</object>
		<object>
			<name><![CDATA[XMLParameters]]></name>
		</object>
		<object>
			<name><![CDATA[3D Viewer]]></name>
		</object>
		<object>
			<name><![CDATA[xm_mainwindow]]></name>
		</object>
		<object>
			<name><![CDATA[symbol_entity_mgr]]></name>
		</object>
		<object>
			<name><![CDATA[GOCurve]]></name>
		</object>
		<object>
			<name><![CDATA[GOPlotWindow]]></name>
		</object>
		<object>
			<name><![CDATA[GOAxis]]></name>
		</object>
		<object>
			<name><![CDATA[GOPlot]]></name>
		</object>
		<object>
			<name><![CDATA[GOLegend]]></name>
		</object>
		<object>
			<name><![CDATA[GOGrid]]></name>
		</object>
		<object>
			<name><![CDATA[GOColorbar]]></name>
		</object>
		<object>
			<name><![CDATA[ResourceTablePlotStyleCatalog]]></name>
		</object>
		<object>
			<name><![CDATA[Views2D]]></name>
		</object>
		<object>
			<name><![CDATA[PDA_Load_Properties]]></name>
		</object>
		<object>
			<name><![CDATA[PDA_Load_Productions]]></name>
		</object>
		<object>
			<name><![CDATA[PDAPropertiesScriptDefinition]]></name>
		</object>
		<object>
			<name><![CDATA[PDAProductionScriptDefinition]]></name>
		</object>
		<object>
			<name><![CDATA[PDA_MultiCell_Probe_Property]]></name>
		</object>
		<object>
			<name><![CDATA[PDA_Summary_Map]]></name>
		</object>
		<object>
			<name><![CDATA[PDA_Movies]]></name>
		</object>
		<object>
			<name><![CDATA[PDA_MultiCell_Probe_Scenario]]></name>
		</object>
		<object>
			<name><![CDATA[PDA_Production_Sets]]></name>
		</object>
		<object>
			<name><![CDATA[PDAProductionPlotLayout]]></name>
		</object>
		<object>
			<name><![CDATA[PDAProductionPlotDefinition]]></name>
		</object>
		<object>
			<name><![CDATA[PDAProductionPlotSelection]]></name>
		</object>
	</objects>
	<data/>
</project>
