# TXT点数据转NetCDF工具 - 使用说明

## 概述

这是一个独立的Python程序，专门用于将txt格式的点数据（x y z +属性）转换为NetCDF格式。该程序从原始的`point_to_nc_ui.py`中提取核心功能，完全去除了AVF相关内容，专注于处理简单的文本点数据。

## 文件清单

- `txt_to_nc.py` - 主程序文件
- `README_txt_to_nc.md` - 详细使用文档
- `convert_txt_to_nc.bat` - Windows批处理脚本
- `convert_txt_to_nc.sh` - Linux/Mac shell脚本
- `test_data.txt` - 测试数据文件
- `使用说明.md` - 本文件

## 快速开始

### 1. 环境要求

```bash
pip install numpy netcdf4 scipy
```

### 2. 基本使用

```bash
# 最简单的用法
python txt_to_nc.py input.txt output.nc

# 指定参数
python txt_to_nc.py input.txt output.nc --x-size 25 --y-size 25 --z-size 0.3 --variable-name oxygen
```

### 3. 使用便捷脚本

**Windows:**
```cmd
convert_txt_to_nc.bat data.txt result.nc 30 30 0.5 oxygen
```

**Linux/Mac:**
```bash
./convert_txt_to_nc.sh data.txt result.nc 30 30 0.5 oxygen
```

## 输入数据格式

支持的txt文件格式：
```
# 注释行（以#开头）
x1 y1 z1 value1
x2 y2 z2 value2
x3 y3 z3 value3
...
```

要求：
- 至少4列数据：x坐标、y坐标、z坐标、属性值
- 数据以空格或制表符分隔
- 支持注释行和空行

## 主要特点

1. **简单易用** - 去除了复杂的GUI界面和AVF处理逻辑
2. **高效处理** - 使用最近邻插值和KDTree优化
3. **标准输出** - 生成符合CF-1.6标准的NetCDF文件
4. **灵活配置** - 支持自定义网格参数和变量名
5. **多种使用方式** - 命令行、交互式、批处理脚本

## 参数说明

### 网格参数
- `--x-size`: X方向网格大小（米），默认55.0
- `--y-size`: Y方向网格大小（米），默认55.0  
- `--z-size`: Z方向网格大小（米），默认0.6

### 其他参数
- `--variable-name`: 数据变量名，默认"data"
- `--interactive`: 启用交互模式

## 测试验证

程序已通过测试：

1. **功能测试** - 使用`test_data.txt`成功转换
2. **参数测试** - 验证不同网格大小和变量名
3. **脚本测试** - 批处理和shell脚本正常工作

测试结果：
- 成功读取30行测试数据
- 正确创建网格和插值
- 生成有效的NetCDF文件

## 与原程序的区别

### 移除的功能
- ✗ PyQt5 GUI界面
- ✗ AVF文件格式支持
- ✗ AVF格式重构功能
- ✗ 坐标系统转换（高斯-克吕格到地理坐标）
- ✗ 复杂的投影参数设置
- ✗ 边界变量创建

### 保留的核心功能
- ✓ 点数据读取和解析
- ✓ 最近邻插值算法
- ✓ KDTree优化的距离计算
- ✓ NetCDF文件创建
- ✓ 网格参数配置

### 新增的功能
- ✓ 命令行参数支持
- ✓ 便捷的批处理脚本
- ✓ 更清晰的错误处理
- ✓ 简化的配置选项

## 性能特点

- **内存效率** - 按Z切片处理，减少内存占用
- **处理速度** - 使用KDTree加速最近邻查找
- **文件大小** - 支持压缩，减小输出文件
- **数据质量** - 距离阈值过滤，避免过度插值

## 适用场景

1. **科学数据处理** - 将观测点数据转换为网格数据
2. **地质勘探** - 处理钻孔数据、物探数据
3. **环境监测** - 转换传感器点数据
4. **海洋学研究** - 处理CTD站点数据
5. **气象数据** - 转换气象站观测数据

## 注意事项

1. **坐标系统** - 假设输入数据使用投影坐标系统（单位：米）
2. **数据分布** - 点数据分布会影响插值效果
3. **网格密度** - 网格越密集，文件越大，处理时间越长
4. **内存使用** - 大数据集可能需要调整网格参数

## 技术支持

如遇问题，请检查：
1. 输入文件格式是否正确
2. 依赖包是否已安装
3. 网格参数是否合理
4. 系统内存是否充足

程序会提供详细的错误信息和处理日志，便于问题诊断。
