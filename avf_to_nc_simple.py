#!/usr/bin/env python3
"""
AVF转NetCDF工具 - 简化版本（去掉兼容性问题的变量）
"""
import numpy as np
from netCDF4 import Dataset
import os
import re
import sys
from datetime import datetime
from typing import Tuple, Optional, List

def split_id_and_x(token: str, max_id_digits: int = 4, ideal_x_int_len: int = 8, min_coord_val: float = 1e5) -> Optional[Tuple[str, str]]:
    """拆分ID和X坐标粘连的字符串"""
    dot = token.find(".")
    if dot == -1:
        return None

    candidates = []
    for k in range(1, min(max_id_digits, dot) + 1):
        id_part, x_part = token[:k], token[k:]
        if x_part.startswith("0"):
            continue

        try:
            id_val = int(id_part)
            x_val = float(x_part)
        except ValueError:
            continue

        if x_val < min_coord_val:
            continue

        int_len = len(x_part.split(".")[0])
        candidates.append((abs(int_len - ideal_x_int_len), k, id_val, x_val))

    if not candidates:
        return None

    _, k, id_val, x_val = sorted(candidates)[0]
    return str(id_val), f"{x_val:.8f}"

def parse_avf_file(file_path: str) -> List[Tuple[float, float, float, float]]:
    """解析AVF文件，返回(X, Y, Z, VEL)数据点列表"""
    points = []
    current_x = None
    current_y = None
    
    print(f"正在解析AVF文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        line_count = 0
        for line in f:
            line_count += 1
            line = line.strip()
            
            if line.startswith('#') or not line:
                continue
            
            fields = line.split()
            
            # 处理完整的5列数据行
            if len(fields) == 5:
                try:
                    id_val = int(fields[0])
                    x = float(fields[1])
                    y = float(fields[2])
                    z = float(fields[3])
                    vel = float(fields[4])
                    
                    current_x = x
                    current_y = y
                    
                    if vel != -99999.0:
                        points.append((x, y, z, vel))
                        
                except ValueError:
                    continue
            
            # 处理4列数据行（ID+X粘连）
            elif len(fields) == 4 and re.fullmatch(r"\d+\.\d+", fields[0]):
                res = split_id_and_x(fields[0])
                if res:
                    try:
                        id_str, x_str = res
                        x = float(x_str)
                        y = float(fields[1])
                        z = float(fields[2])
                        vel = float(fields[3])
                        
                        current_x = x
                        current_y = y
                        
                        if vel != -99999.0:
                            points.append((x, y, z, vel))
                            
                    except ValueError:
                        continue
            
            # 处理只有深度和速度的行
            elif len(fields) == 2 and current_x is not None and current_y is not None:
                try:
                    z = float(fields[0])
                    vel = float(fields[1])
                    
                    if vel != -99999.0:
                        points.append((current_x, current_y, z, vel))
                        
                except ValueError:
                    continue
    
    print(f"解析完成，提取到 {len(points)} 个有效数据点")
    return points

def convert_avf_to_nc(input_file: str, output_file: str, 
                     x_cell_size: float, y_cell_size: float, z_cell_size: float,
                     projection_params: dict = None):
    """将AVF文件转换为NetCDF格式（简化版本）"""
    
    if projection_params is None:
        projection_params = {
            "type": "gauss_kruger",
            "degree_code": "3", 
            "central_meridian": 102.0
        }
    
    # 解析AVF文件
    points = parse_avf_file(input_file)
    
    if not points:
        print("错误: 未找到有效数据点")
        return
    
    # 转换为numpy数组
    point_data = np.array(points)
    x_coords = point_data[:, 0]
    y_coords = point_data[:, 1]
    z_values = point_data[:, 2]
    vel_values = point_data[:, 3]
    
    # 数据范围统计
    x_min, x_max = np.min(x_coords), np.max(x_coords)
    y_min, y_max = np.min(y_coords), np.max(y_coords)
    z_min, z_max = np.min(z_values), np.max(z_values)
    
    print(f'\n数据范围:')
    print(f'  X: {x_min:.2f} - {x_max:.2f} (范围: {x_max-x_min:.2f}m)')
    print(f'  Y: {y_min:.2f} - {y_max:.2f} (范围: {y_max-y_min:.2f}m)')
    print(f'  Z: {z_min:.2f} - {z_max:.2f} (范围: {z_max-z_min:.2f}m)')
    print(f'  速度: {np.min(vel_values):.2f} - {np.max(vel_values):.2f}')
    
    # 计算网格数量
    x_grid_count = int(np.ceil((x_max - x_min) / x_cell_size)) + 1
    y_grid_count = int(np.ceil((y_max - y_min) / y_cell_size)) + 1
    z_grid_count = int(np.ceil((z_max - z_min) / z_cell_size)) + 1
    
    print(f'\n网格设置:')
    print(f'  X方向: {x_grid_count}个网格 (间距: {x_cell_size}m)')
    print(f'  Y方向: {y_grid_count}个网格 (间距: {y_cell_size}m)')
    print(f'  Z方向: {z_grid_count}个网格 (间距: {z_cell_size}m)')
    print(f'  总网格点: {x_grid_count * y_grid_count * z_grid_count:,}')
    
    # 估算文件大小
    estimated_size_mb = (x_grid_count * y_grid_count * z_grid_count * 4) / (1024*1024)
    print(f'  预估文件大小: {estimated_size_mb:.1f} MB')
    
    # 创建网格坐标
    x_padding = x_cell_size * 0.5
    y_padding = y_cell_size * 0.5
    z_padding = z_cell_size * 0.5
    
    x_grid = np.linspace(x_min - x_padding, x_max + x_padding, x_grid_count)
    y_grid = np.linspace(y_min - y_padding, y_max + y_padding, y_grid_count)
    z_grid = np.linspace(z_min - z_padding, z_max + z_padding, z_grid_count)
    
    x_spacing = (x_grid[-1] - x_grid[0]) / (x_grid_count - 1) if x_grid_count > 1 else x_cell_size
    y_spacing = (y_grid[-1] - y_grid[0]) / (y_grid_count - 1) if y_grid_count > 1 else y_cell_size
    z_spacing = (z_grid[-1] - z_grid[0]) / (z_grid_count - 1) if z_grid_count > 1 else z_cell_size
    
    print(f'  实际网格间距: X={x_spacing:.2f}m, Y={y_spacing:.2f}m, Z={z_spacing:.2f}m')
    
    # 创建NetCDF文件
    if os.path.exists(output_file):
        os.remove(output_file)
    
    print(f'\n创建NetCDF文件: {output_file}')
    nc = Dataset(output_file, 'w', format='NETCDF4')
    
    try:
        # 创建维度（只创建必要的维度）
        nc.createDimension('x', x_grid_count)
        nc.createDimension('y', y_grid_count)
        nc.createDimension('z', z_grid_count)
        
        # 创建坐标变量（简化版本，去掉边界变量）
        x_var = nc.createVariable('x', 'f8', ('x',))
        y_var = nc.createVariable('y', 'f8', ('y',))
        z_var = nc.createVariable('z', 'f8', ('z',))
        
        # 设置坐标变量属性（简化版本）
        x_var.standard_name = 'projection_x_coordinate'
        x_var.long_name = 'x coordinate of projection'
        x_var.units = 'Meter'
        x_var.axis = 'X'
        
        y_var.standard_name = 'projection_y_coordinate'
        y_var.long_name = 'y coordinate of projection'
        y_var.units = 'Meter'
        y_var.axis = 'Y'
        
        z_var.standard_name = 'height'
        z_var.long_name = 'height above reference level'
        z_var.units = 'Meter'
        z_var.positive = 'up'
        z_var.axis = 'Z'
        
        # 设置坐标值
        x_var[:] = x_grid
        y_var[:] = y_grid
        z_var[:] = z_grid
        
        # 创建数据变量
        fill_value = -9999.0
        velocity = nc.createVariable('Velocity', 'f4', ('z', 'y', 'x'), 
                                   fill_value=fill_value, chunksizes=None, zlib=True, complevel=1)
        
        # 设置数据变量属性（简化版本）
        velocity.standard_name = 'velocity'
        velocity.long_name = 'Velocity from GOCAD AVF Model'
        velocity.coordinates = 'x y z'
        velocity.valid_range = np.array([np.min(vel_values), np.max(vel_values)], dtype=np.float32)
        velocity.scale_factor = 1.0
        velocity.add_offset = 0.0
        velocity.units = 'm/s'
        
        # 生成投影字符串
        if projection_params["type"] == "gauss_kruger":
            degree_code = projection_params["degree_code"]
            central_meridian = projection_params["central_meridian"]
            if degree_code == "3":
                zone = int((central_meridian + 1.5) / 3)
                esri_pe_string = f'PROJCS["CGCS2000_3_Degree_GK_Zone_{zone}",GEOGCS["GCS_China_Geodetic_Coordinate_System_2000",DATUM["D_China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Gauss_Kruger"],PARAMETER["False_Easting",500000.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",{central_meridian}],PARAMETER["Scale_Factor",1.0],PARAMETER["Latitude_Of_Origin",0.0],UNIT["Meter",1.0]]'
            else:
                esri_pe_string = f'PROJCS["CGCS2000_3_Degree_GK_CM_{int(central_meridian)}E",GEOGCS["GCS_China_Geodetic_Coordinate_System_2000",DATUM["D_China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Gauss_Kruger"],PARAMETER["False_Easting",500000.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",{central_meridian}],PARAMETER["Scale_Factor",1.0],PARAMETER["Latitude_Of_Origin",0.0],UNIT["Meter",1.0]]'
        
        # 将投影信息添加到数据变量
        velocity.esri_pe_string = esri_pe_string
        
        # 初始化网格数据
        grid_data = np.full((z_grid_count, y_grid_count, x_grid_count), fill_value, dtype=np.float32)
        
        print('映射数据点到网格...')
        mapped_count = 0
        outside_count = 0
        
        for i in range(len(x_coords)):
            # 计算网格索引
            x_idx = int(np.round((x_coords[i] - x_grid[0]) / x_spacing))
            y_idx = int(np.round((y_coords[i] - y_grid[0]) / y_spacing))
            z_idx = int(np.round((z_values[i] - z_grid[0]) / z_spacing))
            
            # 检查索引是否在有效范围内
            if (0 <= x_idx < x_grid_count and 
                0 <= y_idx < y_grid_count and 
                0 <= z_idx < z_grid_count):
                
                # 如果网格点已有数据，取平均值
                if grid_data[z_idx, y_idx, x_idx] != fill_value:
                    grid_data[z_idx, y_idx, x_idx] = (grid_data[z_idx, y_idx, x_idx] + vel_values[i]) / 2
                else:
                    grid_data[z_idx, y_idx, x_idx] = vel_values[i]
                mapped_count += 1
            else:
                outside_count += 1
        
        print(f'数据映射完成: {mapped_count} 个点成功映射, {outside_count} 个点超出范围')
        
        # 统计有效数据
        valid_count = np.sum(grid_data != fill_value)
        total_cells = x_grid_count * y_grid_count * z_grid_count
        coverage = valid_count / total_cells * 100
        
        print(f'网格统计: {valid_count:,} 个有效网格点 / {total_cells:,} 总网格点 (覆盖率: {coverage:.2f}%)')
        
        # 写入数据到NetCDF
        print('写入NetCDF文件...')
        velocity[:, :, :] = grid_data
        
        # 设置全局属性（简化版本）
        nc.Conventions = 'CF-1.6'
        nc.title = "GOCAD AVF Velocity Data"
        nc.source = "GOCAD AVF model exported as velocity data"
        nc.history = f"Created from GOCAD AVF data on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        nc.comment = "Original GOCAD AVF data preserved without interpolation"
        nc.institution = "GOCAD Data Processing"
        nc.Source_Software = 'Python NetCDF4'
        
        # 添加投影信息到全局属性
        nc.spatial_ref = esri_pe_string
        nc.geotransform = f"{x_grid[0]} {x_spacing} 0.0 {y_grid[-1]} 0.0 {-y_spacing}"
        
        # 添加网格参数到全局属性
        nc.grid_cell_size_x = x_cell_size
        nc.grid_cell_size_y = y_cell_size
        nc.grid_cell_size_z = z_cell_size
        
        # 添加坐标系信息
        if projection_params["type"] == "gauss_kruger":
            if projection_params["degree_code"] == "3":
                zone = int((projection_params["central_meridian"] + 1.5) / 3)
                nc.references = f"CGCS2000_3_Degree_GK_Zone_{zone}"
            else:
                nc.references = f"CGCS2000_3_Degree_GK_CM_{int(projection_params['central_meridian'])}E"
        
        print('NetCDF文件创建完成!')
        
    finally:
        nc.close()

def get_user_parameters():
    """获取用户自定义参数"""
    print("\n=== 网格参数设置 ===")
    print("网格越小 = 分辨率越高 = 文件越大")
    print("推荐设置:")
    print("  高精度: X=25m, Y=25m, Z=0.3m")
    print("  中精度: X=55m, Y=55m, Z=0.6m (默认)")
    print("  低精度: X=100m, Y=100m, Z=1.0m")
    print()
    
    # X方向网格大小
    while True:
        try:
            x_input = input(f"X方向网格大小 (米) [默认: 55]: ").strip()
            x_cell_size = 55.0 if not x_input else float(x_input)
            if x_cell_size > 0:
                break
            else:
                print("错误: 网格大小必须大于0")
        except ValueError:
            print("错误: 请输入有效数字")
    
    # Y方向网格大小
    while True:
        try:
            y_input = input(f"Y方向网格大小 (米) [默认: 55]: ").strip()
            y_cell_size = 55.0 if not y_input else float(y_input)
            if y_cell_size > 0:
                break
            else:
                print("错误: 网格大小必须大于0")
        except ValueError:
            print("错误: 请输入有效数字")
    
    # Z方向网格大小
    while True:
        try:
            z_input = input(f"Z方向网格大小 (米) [默认: 0.6]: ").strip()
            z_cell_size = 0.6 if not z_input else float(z_input)
            if z_cell_size > 0:
                break
            else:
                print("错误: 网格大小必须大于0")
        except ValueError:
            print("错误: 请输入有效数字")
    
    return x_cell_size, y_cell_size, z_cell_size

def main():
    print("AVF转NetCDF工具 - 简化版本（兼容性优化）")
    print("=" * 50)
    
    # 获取文件路径
    if len(sys.argv) >= 3:
        input_file = sys.argv[1]
        output_file = sys.argv[2]
        # 命令行模式使用默认参数
        x_cell_size, y_cell_size, z_cell_size = 55.0, 55.0, 0.6
    else:
        input_file = input("请输入AVF文件路径: ").strip()
        output_file = input("请输入输出NC文件路径: ").strip()
        # 交互模式获取自定义参数
        x_cell_size, y_cell_size, z_cell_size = get_user_parameters()
    
    if not os.path.exists(input_file):
        print(f"错误: 找不到输入文件 {input_file}")
        sys.exit(1)
    
    # 投影参数
    projection_params = {
        "type": "gauss_kruger",
        "degree_code": "3",
        "central_meridian": 102.0
    }
    
    print(f"\n=== 转换参数 ===")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"网格大小: X={x_cell_size}m, Y={y_cell_size}m, Z={z_cell_size}m")
    print(f"投影: {projection_params['type']}, 度带: {projection_params['degree_code']}, 中央子午线: {projection_params['central_meridian']}")
    
    try:
        convert_avf_to_nc(input_file, output_file, x_cell_size, y_cell_size, z_cell_size, projection_params)
        print(f"\n✅ 转换完成! 输出文件: {output_file}")
    except Exception as e:
        print(f"\n❌ 转换失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 