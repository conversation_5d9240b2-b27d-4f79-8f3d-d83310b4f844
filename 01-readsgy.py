import segyio
import numpy as np
import matplotlib.pyplot as plt

def save_to_npy(segy_data, filename='mig01_data.npy'):
    """将地震数据保存为npy文件"""
    
    print(f"正在保存数据为 {filename}...")
    
    # 使用inline和crossline的数量重塑数据
    try:
        # 尝试创建规则网格
        cube = np.full((len(segy_data['unique_inlines']), 
                       len(segy_data['unique_crosslines']), 
                       segy_data['data'].shape[1]), np.nan)
        
        # 填充数据
        for i, (il, xl) in enumerate(zip(segy_data['inlines'], segy_data['crosslines'])):
            il_idx = np.where(segy_data['unique_inlines'] == il)[0]
            xl_idx = np.where(segy_data['unique_crosslines'] == xl)[0]
            if len(il_idx) > 0 and len(xl_idx) > 0:
                cube[il_idx[0], xl_idx[0], :] = segy_data['data'][i, :]
        
        # 归一化数据以便于可视化
        cube_norm = (cube - np.nanmin(cube)) / (np.nanmax(cube) - np.nanmin(cube)) * 255
        
        # 保存数据
        np.save(filename, cube_norm)
        print(f"数据已保存: {cube_norm.shape}")
        
        return cube_norm
        
    except Exception as e:
        print(f"保存npy文件时出错: {e}")
        return None

def visualize_3d(cube_data, segy_data):
    """使用pyvista进行3D可视化"""
    
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QSlider, QVBoxLayout, QWidget, QLabel, QHBoxLayout
        from PyQt5.QtCore import Qt
        from pyvistaqt import QtInteractor
        import pyvista as pv
        import sys
        
        pv.global_theme.allow_empty_mesh = True
        
        class SeismicViewer(QMainWindow):
            def __init__(self, data, segy_info):
                super().__init__()
                self.data = data
                self.segy_info = segy_info
                self.setWindowTitle("地震数据3D可视化")
                self.setGeometry(100, 100, 1000, 800)
                self.initUI()

            def initUI(self):
                self.setFixedSize(1000, 800)
                self.plotter = QtInteractor(self)
                central_widget = QWidget(self)
                self.setCentralWidget(central_widget)
                main_layout = QVBoxLayout(central_widget)
                main_layout.addWidget(self.plotter.interactor)
                
                # 控制面板
                control_layout = QVBoxLayout()
                
                # Inline滑块
                inline_layout = QHBoxLayout()
                inline_layout.addWidget(QLabel("Inline:"))
                self.slider_inline = QSlider(Qt.Horizontal)
                self.slider_inline.setMaximum(self.data.shape[0] - 1)
                self.slider_inline.setValue(self.data.shape[0] // 2)
                self.label_inline = QLabel(f"{self.segy_info['unique_inlines'][self.data.shape[0] // 2]}")
                inline_layout.addWidget(self.slider_inline)
                inline_layout.addWidget(self.label_inline)
                control_layout.addLayout(inline_layout)
                
                # Crossline滑块
                crossline_layout = QHBoxLayout()
                crossline_layout.addWidget(QLabel("Crossline:"))
                self.slider_crossline = QSlider(Qt.Horizontal)
                self.slider_crossline.setMaximum(self.data.shape[1] - 1)
                self.slider_crossline.setValue(self.data.shape[1] // 2)
                self.label_crossline = QLabel(f"{self.segy_info['unique_crosslines'][self.data.shape[1] // 2]}")
                crossline_layout.addWidget(self.slider_crossline)
                crossline_layout.addWidget(self.label_crossline)
                control_layout.addLayout(crossline_layout)
                
                # Time滑块
                time_layout = QHBoxLayout()
                time_layout.addWidget(QLabel("Time:"))
                self.slider_time = QSlider(Qt.Horizontal)
                self.slider_time.setMaximum(self.data.shape[2] - 1)
                self.slider_time.setValue(self.data.shape[2] // 2)
                self.label_time = QLabel(f"{self.segy_info['samples'][self.data.shape[2] // 2]:.1f}ms")
                time_layout.addWidget(self.slider_time)
                time_layout.addWidget(self.label_time)
                control_layout.addLayout(time_layout)
                
                main_layout.addLayout(control_layout)
                
                # 连接滑块信号
                self.slider_inline.valueChanged.connect(self.update_inline_label)
                self.slider_crossline.valueChanged.connect(self.update_crossline_label)
                self.slider_time.valueChanged.connect(self.update_time_label)
                self.slider_inline.valueChanged.connect(self.update_slice)
                self.slider_crossline.valueChanged.connect(self.update_slice)
                self.slider_time.valueChanged.connect(self.update_slice)
                
                self.setup_3d_data()
                self.update_slice()

            def setup_3d_data(self):
                """设置3D数据网格"""
                self.grid = pv.ImageData()
                self.grid.dimensions = self.data.shape
                self.grid.origin = (0, 0, 0)
                self.grid.spacing = (1, 1, self.segy_info['dt'] / 1000)  # 使用采样率
                self.grid.point_data["Amplitudes"] = self.data.flatten(order="F")

            def update_inline_label(self):
                idx = self.slider_inline.value()
                if idx < len(self.segy_info['unique_inlines']):
                    self.label_inline.setText(f"{self.segy_info['unique_inlines'][idx]}")

            def update_crossline_label(self):
                idx = self.slider_crossline.value()
                if idx < len(self.segy_info['unique_crosslines']):
                    self.label_crossline.setText(f"{self.segy_info['unique_crosslines'][idx]}")

            def update_time_label(self):
                idx = self.slider_time.value()
                if idx < len(self.segy_info['samples']):
                    self.label_time.setText(f"{self.segy_info['samples'][idx]:.1f}ms")

            def update_slice(self):
                """更新3D切片显示"""
                self.plotter.clear()
                
                inline_idx = self.slider_inline.value()
                crossline_idx = self.slider_crossline.value()
                time_idx = self.slider_time.value()
                
                # Inline切片 (YZ平面)
                slice_inline = self.grid.slice(normal='x', origin=(inline_idx, 0, 0))
                if slice_inline.n_points > 0:
                    self.plotter.add_mesh(slice_inline, cmap='seismic', 
                                        clim=[0, 255], opacity=0.8, label='Inline')
                
                # Crossline切片 (XZ平面)
                slice_crossline = self.grid.slice(normal='y', origin=(0, crossline_idx, 0))
                if slice_crossline.n_points > 0:
                    self.plotter.add_mesh(slice_crossline, cmap='seismic', 
                                        clim=[0, 255], opacity=0.8, label='Crossline')
                
                # Time切片 (XY平面)
                slice_time = self.grid.slice(normal='z', origin=(0, 0, time_idx))
                if slice_time.n_points > 0:
                    self.plotter.add_mesh(slice_time, cmap='seismic', 
                                        clim=[0, 255], opacity=0.8, label='Time')
                
                # 设置相机视角
                self.plotter.view_isometric()
                self.plotter.show()

        # 启动3D可视化窗口
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        window = SeismicViewer(cube_data, segy_data)
        window.show()
        
        print("3D可视化窗口已启动")
        print("使用滑块控制不同方向的切片显示")
        
        return app, window
        
    except ImportError as e:
        print(f"3D可视化需要安装: pip install pyvista pyvistaqt PyQt5")
        print(f"错误: {e}")
        return None, None
    except Exception as e:
        print(f"3D可视化出错: {e}")
        return None, None

def read_sgy_file(filename):
    """读取SEG-Y文件并提取数据和道头信息"""
    
    with segyio.open(filename, strict=False) as segy:
        # 获取基本信息
        print(f"文件: {filename}")
        print(f"道数: {segy.tracecount}")
        print(f"采样点数: {len(segy.samples)}")
        print(f"采样率: {segy.bin[segyio.BinField.Interval]} 微秒")
        
        # 读取inline（字节9）和crossline（字节21）信息
        inlines = [segy.header[i][9] for i in range(segy.tracecount)]
        crosslines = [segy.header[i][21] for i in range(segy.tracecount)]
        x_coords = [segy.header[i][73] for i in range(segy.tracecount)]
        y_coords = [segy.header[i][77] for i in range(segy.tracecount)]
        
        # 转换为numpy数组
        inlines = np.array(inlines)
        crosslines = np.array(crosslines)
        x_coords = np.array(x_coords)
        y_coords = np.array(y_coords)
        
        # 获取唯一的inline和crossline值
        unique_inlines = np.unique(inlines)
        unique_crosslines = np.unique(crosslines)
        
        print(f"Inline范围: {min(unique_inlines)} - {max(unique_inlines)}")
        print(f"Crossline范围: {min(unique_crosslines)} - {max(unique_crosslines)}")
        print(f"Inline数量: {len(unique_inlines)}")
        print(f"Crossline数量: {len(unique_crosslines)}")

        # 读取数据
        seismic_data = np.stack([t.astype(float) for t in segy.trace])
        
        return {
            'data': seismic_data,
            'inlines': inlines,
            'crosslines': crosslines,
            'x_coords': x_coords,
            'y_coords': y_coords,
            'unique_inlines': unique_inlines,
            'unique_crosslines': unique_crosslines,
            'samples': segy.samples,
            'dt': segy.bin[segyio.BinField.Interval]
        }

def visualize_seismic(segy_data, target_inline=None, target_crossline=None, time_slice=0):
    """可视化地震数据"""
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle('SEG-Y 数据可视化', fontsize=16)
    
    print(f"数据形状: {segy_data['data'].shape}")
    print(f"理论网格大小: {len(segy_data['unique_inlines'])} × {len(segy_data['unique_crosslines'])} = {len(segy_data['unique_inlines']) * len(segy_data['unique_crosslines'])}")
    print(f"实际道数: {len(segy_data['data'])}")
    
    # 如果没有指定，使用中间值
    if target_inline is None:
        target_inline = segy_data['unique_inlines'][len(segy_data['unique_inlines'])//2]
    if target_crossline is None:
        target_crossline = segy_data['unique_crosslines'][len(segy_data['unique_crosslines'])//2]
    
    # 1. 显示指定inline的数据
    if target_inline in segy_data['unique_inlines']:
        inline_indices = np.where(segy_data['inlines'] == target_inline)[0]
        inline_data = segy_data['data'][inline_indices, :]
        print(f"找到Inline {target_inline}: {len(inline_indices)}道")
    else:
        print(f"未找到Inline {target_inline}, 使用中间Inline {segy_data['unique_inlines'][len(segy_data['unique_inlines'])//2]}")
        target_inline = segy_data['unique_inlines'][len(segy_data['unique_inlines'])//2]
        inline_indices = np.where(segy_data['inlines'] == target_inline)[0]
        inline_data = segy_data['data'][inline_indices, :]
    
    im1 = axes[0,0].imshow(inline_data.T, aspect='auto', cmap='seismic')
    axes[0,0].set_title(f'Inline {target_inline} ({len(inline_indices)}道)')
    axes[0,0].set_xlabel('道索引')
    axes[0,0].set_ylabel('时间 (样点)')
    plt.colorbar(im1, ax=axes[0,0])
    
    # 2. 显示指定crossline的数据
    if target_crossline in segy_data['unique_crosslines']:
        crossline_indices = np.where(segy_data['crosslines'] == target_crossline)[0]
        crossline_data = segy_data['data'][crossline_indices, :]
        print(f"找到Crossline {target_crossline}: {len(crossline_indices)}道")
    else:
        print(f"未找到Crossline {target_crossline}, 使用中间Crossline {segy_data['unique_crosslines'][len(segy_data['unique_crosslines'])//2]}")
        target_crossline = segy_data['unique_crosslines'][len(segy_data['unique_crosslines'])//2]
        crossline_indices = np.where(segy_data['crosslines'] == target_crossline)[0]
        crossline_data = segy_data['data'][crossline_indices, :]
    
    im2 = axes[0,1].imshow(crossline_data.T, aspect='auto', cmap='seismic')
    axes[0,1].set_title(f'Crossline {target_crossline} ({len(crossline_indices)}道)')
    axes[0,1].set_xlabel('道索引')
    axes[0,1].set_ylabel('时间 (样点)')
    plt.colorbar(im2, ax=axes[0,1])
    
    # 3. 显示时间切片（使用真实坐标，不插值）
    max_time = segy_data['data'].shape[1] - 1
    if time_slice > max_time:
        time_slice = max_time // 2
        print(f"时间切片超出范围，使用中间切片: {time_slice}")
    
    slice_data = segy_data['data'][:, time_slice]
    
    # 直接使用scatter plot显示真实坐标位置的数据
    scatter = axes[0,2].scatter(segy_data['x_coords'], segy_data['y_coords'], 
                               c=slice_data, cmap='seismic', s=1, alpha=0.8)
    
    axes[0,2].set_title(f'时间切片 (样点 {time_slice}) - 真实坐标')
    axes[0,2].set_xlabel('X坐标')
    axes[0,2].set_ylabel('Y坐标')
    axes[0,2].set_aspect('equal')
    
    # 添加inline和crossline网格线
    for il in segy_data['unique_inlines'][::10]:  # 每10条显示一条
        il_indices = np.where(segy_data['inlines'] == il)[0]
        if len(il_indices) > 0:
            il_x = segy_data['x_coords'][il_indices]
            il_y = segy_data['y_coords'][il_indices]
            # 按crossline排序以正确连线
            sort_idx = np.argsort(segy_data['crosslines'][il_indices])
            axes[0,2].plot(il_x[sort_idx], il_y[sort_idx], 'k-', alpha=0.3, linewidth=0.5)
    
    for xl in segy_data['unique_crosslines'][::10]:  # 每10条显示一条
        xl_indices = np.where(segy_data['crosslines'] == xl)[0]
        if len(xl_indices) > 0:
            xl_x = segy_data['x_coords'][xl_indices]
            xl_y = segy_data['y_coords'][xl_indices]
            # 按inline排序以正确连线
            sort_idx = np.argsort(segy_data['inlines'][xl_indices])
            axes[0,2].plot(xl_x[sort_idx], xl_y[sort_idx], 'k-', alpha=0.3, linewidth=0.5)
    
    plt.colorbar(scatter, ax=axes[0,2])
    
    # 4. 显示单道波形
    trace_idx = len(segy_data['data']) // 2  # 选择中间的道
    trace_data = segy_data['data'][trace_idx, :]
    time_axis = segy_data['samples']
    
    axes[1,0].plot(trace_data, time_axis)
    axes[1,0].set_title(f'单道波形 (道号: {trace_idx})')
    axes[1,0].set_xlabel('振幅')
    axes[1,0].set_ylabel('时间 (毫秒)')
    axes[1,0].invert_yaxis()
    axes[1,0].grid(True)
    
    # 5. 显示振幅分布直方图
    all_amplitudes = segy_data['data'].flatten()
    # 移除极值以获得更好的显示
    p1, p99 = np.percentile(all_amplitudes, [1, 99])
    filtered_amps = all_amplitudes[(all_amplitudes >= p1) & (all_amplitudes <= p99)]
    
    axes[1,1].hist(filtered_amps, bins=100, alpha=0.7)
    axes[1,1].set_title('振幅分布直方图')
    axes[1,1].set_xlabel('振幅')
    axes[1,1].set_ylabel('频次')
    axes[1,1].grid(True)
    
    # 6. 显示数据体概览
    # 显示所有inline的第一道振幅
    first_traces = []
    for il in segy_data['unique_inlines']:
        il_indices = np.where(segy_data['inlines'] == il)[0]
        if len(il_indices) > 0:
            first_traces.append(segy_data['data'][il_indices[0], 0])
        else:
            first_traces.append(0)
    
    axes[1,2].plot(segy_data['unique_inlines'], first_traces, 'o-', markersize=2)
    axes[1,2].set_title('各Inline第一道振幅')
    axes[1,2].set_xlabel('Inline号')
    axes[1,2].set_ylabel('振幅')
    axes[1,2].grid(True)
    
    plt.tight_layout()
    plt.show()

def main():
    """主函数"""
    filename = 'mig01.sgy'
    
    # 可在此处修改显示参数
    target_inline = 1206      # 指定要显示的inline，设为None使用中间值
    target_crossline = 1226   # 指定要显示的crossline，设为None使用中间值
    time_slice = 300          # 指定时间切片位置（样点索引），从0开始
    
    # 控制选项
    save_npy = True          # 是否保存为npy文件
    show_3d = True           # 是否显示3D可视化
    
    try:
        # 读取SEG-Y文件
        print("正在读取SEG-Y文件...")
        segy_data = read_sgy_file(filename)
        
        print(f"\n可用Inline范围: {min(segy_data['unique_inlines'])} - {max(segy_data['unique_inlines'])}")
        print(f"可用Crossline范围: {min(segy_data['unique_crosslines'])} - {max(segy_data['unique_crosslines'])}")
        print(f"可用时间切片范围: 0 - {segy_data['data'].shape[1]-1}")
        
        # 保存为npy文件
        cube_data = None
        if save_npy:
            cube_data = save_to_npy(segy_data, 'mig01_data.npy')
        
        # 2D可视化
        print(f"\n正在生成2D可视化图像...")
        print(f"显示参数: Inline={target_inline}, Crossline={target_crossline}, 时间切片={time_slice}")
        visualize_seismic(segy_data, target_inline, target_crossline, time_slice)
        
        # 3D可视化
        if show_3d and cube_data is not None:
            print(f"\n启动3D可视化...")
            app, window = visualize_3d(cube_data, segy_data)
            if app is not None and window is not None:
                try:
                    app.exec_()
                except SystemExit:
                    pass
        elif show_3d and cube_data is None:
            print("3D可视化需要先成功保存npy文件")
        
        print("完成!")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()