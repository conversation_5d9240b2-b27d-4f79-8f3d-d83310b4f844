#!/bin/bash

echo "TXT点数据转NetCDF工具"
echo "====================="
echo

if [ $# -eq 0 ]; then
    echo "用法: $0 input.txt [output.nc] [x_size] [y_size] [z_size] [variable_name]"
    echo
    echo "参数说明:"
    echo "  input.txt     - 输入的TXT文件路径 (必需)"
    echo "  output.nc     - 输出的NetCDF文件路径 (可选，默认为input.nc)"
    echo "  x_size        - X方向网格大小(米)，默认55.0"
    echo "  y_size        - Y方向网格大小(米)，默认55.0"
    echo "  z_size        - Z方向网格大小(米)，默认0.6"
    echo "  variable_name - 数据变量名，默认data"
    echo
    echo "示例:"
    echo "  $0 data.txt"
    echo "  $0 data.txt result.nc"
    echo "  $0 data.txt result.nc 25 25 0.3 oxygen"
    echo
    exit 1
fi

INPUT_FILE="$1"
OUTPUT_FILE="${2:-${INPUT_FILE%.*}.nc}"
X_SIZE="${3:-55.0}"
Y_SIZE="${4:-55.0}"
Z_SIZE="${5:-0.6}"
VAR_NAME="${6:-data}"

echo "转换参数:"
echo "  输入文件: $INPUT_FILE"
echo "  输出文件: $OUTPUT_FILE"
echo "  网格大小: X=${X_SIZE}m, Y=${Y_SIZE}m, Z=${Z_SIZE}m"
echo "  变量名: $VAR_NAME"
echo

python txt_to_nc.py "$INPUT_FILE" "$OUTPUT_FILE" --x-size "$X_SIZE" --y-size "$Y_SIZE" --z-size "$Z_SIZE" --variable-name "$VAR_NAME"

if [ $? -eq 0 ]; then
    echo
    echo "转换成功完成！"
else
    echo
    echo "转换失败，请检查错误信息。"
    exit 1
fi
