from PyQt5.QtWidgets import QApplication, QMainWindow, QSlider, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from pyvistaqt import QtInteractor
import pyvista as pv
import numpy as np
import sys
pv.global_theme.allow_empty_mesh = True

class ViewerWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("数据展示")
        self.setGeometry(100, 100, 800, 600)
        self.initUI()

    def initUI(self):
        self.setFixedSize(800, 600)
        self.plotter = QtInteractor(self)
        central_widget = QWidget(self)
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.addWidget(self.plotter.interactor)
        
        # 创建X方向的滑块
        self.slider_x = QSlider(Qt.Horizontal)
        self.slider_x.setMaximum(100)  # 根据实际数据调整
        layout.addWidget(self.slider_x)
        
        # 创建Y方向的滑块
        self.slider_y = QSlider(Qt.Horizontal)
        self.slider_y.setMaximum(100)  # 根据实际数据调整
        layout.addWidget(self.slider_y)
        
        # 创建Z方向的滑块
        self.slider_z = QSlider(Qt.Horizontal)
        self.slider_z.setMaximum(100)  # 根据实际数据调整
        layout.addWidget(self.slider_z)
        
        # 连接滑块的信号
        self.slider_x.valueChanged.connect(self.update_slice)
        self.slider_y.valueChanged.connect(self.update_slice)
        self.slider_z.valueChanged.connect(self.update_slice)
        
        self.load_and_visualize_data()

    def load_and_visualize_data(self):
        # 加载数据
        values = np.load('DATA/volve.npy')
        self.grid = pv.ImageData()
        self.grid.dimensions = values.shape
        self.grid.origin = (0, 0, 0)
        self.grid.spacing = (1, 1, 1)
        self.grid.point_data["Amplitudes"] = values.flatten(order="F")
        
        # 显示初始切片
        self.update_slice()

    def update_slice(self):
        self.plotter.clear()
        x = self.slider_x.value()
        y = self.slider_y.value()
        z = self.slider_z.value()
        
        # 生成X方向的切片
        slice_x = self.grid.slice(normal='x', origin=(x, 0, 0))
        if slice_x.n_points > 0:
            self.plotter.add_mesh(slice_x, cmap='RdGy', clim=[-5, 5], label='X-Slice')
        
        # 生成Y方向的切片
        slice_y = self.grid.slice(normal='y', origin=(0, y, 0))
        if slice_y.n_points > 0:
            self.plotter.add_mesh(slice_y, cmap='RdGy', clim=[-5, 5], label='Y-Slice')
        
        # 生成Z方向的切片
        slice_z = self.grid.slice(normal='z', origin=(0, 0, z))
        if slice_z.n_points > 0:
            self.plotter.add_mesh(slice_z, cmap='RdGy', clim=[-5, 5], label='Z-Slice')
        
        self.plotter.show()



if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = ViewerWindow()
    window.show()
    sys.exit(app.exec_())
