@echo off
echo TXT点数据转NetCDF工具
echo =====================
echo.

if "%1"=="" (
    echo 用法: convert_txt_to_nc.bat input.txt [output.nc] [x_size] [y_size] [z_size] [variable_name]
    echo.
    echo 参数说明:
    echo   input.txt     - 输入的TXT文件路径 ^(必需^)
    echo   output.nc     - 输出的NetCDF文件路径 ^(可选，默认为input.nc^)
    echo   x_size        - X方向网格大小^(米^)，默认55.0
    echo   y_size        - Y方向网格大小^(米^)，默认55.0
    echo   z_size        - Z方向网格大小^(米^)，默认0.6
    echo   variable_name - 数据变量名，默认data
    echo.
    echo 示例:
    echo   convert_txt_to_nc.bat data.txt
    echo   convert_txt_to_nc.bat data.txt result.nc
    echo   convert_txt_to_nc.bat data.txt result.nc 25 25 0.3 oxygen
    echo.
    pause
    exit /b 1
)

set INPUT_FILE=%1
set OUTPUT_FILE=%2
set X_SIZE=%3
set Y_SIZE=%4
set Z_SIZE=%5
set VAR_NAME=%6

if "%OUTPUT_FILE%"=="" (
    for %%f in ("%INPUT_FILE%") do set OUTPUT_FILE=%%~nf.nc
)

if "%X_SIZE%"=="" set X_SIZE=55.0
if "%Y_SIZE%"=="" set Y_SIZE=55.0
if "%Z_SIZE%"=="" set Z_SIZE=0.6
if "%VAR_NAME%"=="" set VAR_NAME=data

echo 转换参数:
echo   输入文件: %INPUT_FILE%
echo   输出文件: %OUTPUT_FILE%
echo   网格大小: X=%X_SIZE%m, Y=%Y_SIZE%m, Z=%Z_SIZE%m
echo   变量名: %VAR_NAME%
echo.

python txt_to_nc.py "%INPUT_FILE%" "%OUTPUT_FILE%" --x-size %X_SIZE% --y-size %Y_SIZE% --z-size %Z_SIZE% --variable-name %VAR_NAME%

if %ERRORLEVEL% EQU 0 (
    echo.
    echo 转换成功完成！
) else (
    echo.
    echo 转换失败，请检查错误信息。
)

pause
