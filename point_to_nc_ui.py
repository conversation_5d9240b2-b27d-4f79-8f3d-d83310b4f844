import numpy as np
from netCDF4 import Dataset
import os
from scipy.spatial import cKDTree
from scipy.interpolate import griddata
import sys
from datetime import datetime
import pathlib
import re
from typing import Tuple, Optional
import avf_to_nc_simple
import math
import platform
import ctypes
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QGridLayout, QLabel, QLineEdit, 
                             QPushButton, QComboBox, QTextEdit, QGroupBox,
                             QFileDialog, QMessageBox, QDoubleSpinBox)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont

# ---------------------------- 坐标转换功能 ----------------------------
class CoordinateConverter:
    """坐标转换核心类（从 obj_converter_gui.py 引入）"""
    
    def __init__(self):
        # 椭球参数
        self.ellipsoids = {
            'xian80': {'a': 6378140.0, 'f': 1/298.257},
            'beijing54': {'a': 6378245.0, 'f': 1/298.3},
            'cgcs2000': {'a': 6378137.0, 'f': 1/298.257222101}
        }
    
    def gauss_kruger_to_geographic(self, x, y, zone, ellipsoid='cgcs2000'):
        """
        高斯-克吕格投影坐标转换为地理坐标
        """
        # 获取椭球参数
        ellip = self.ellipsoids[ellipsoid]
        a = ellip['a']
        f = ellip['f']
        
        # 计算偏心率
        e2 = 2*f - f*f
        e_prime2 = e2 / (1 - e2)
        
        # 3度带中央经线
        central_meridian = zone * 3.0
        
        # 投影参数
        false_easting = 500000.0
        false_northing = 0.0
        scale_factor = 1.0
        
        # 移除偏移
        x_relative = x - zone * 1000000 - false_easting
        y_relative = y - false_northing
        
        # 计算足点纬度
        M = y_relative / scale_factor
        
        # 椭圆弧长公式系数
        A = a * (1 - e2 * (1.0/4 + e2 * (3.0/64 + 5.0*e2/256)))
        B = a * e2 * (3.0/8 + e2 * (3.0/32 + 45.0*e2/1024))
        C = a * e2*e2 * (15.0/256 + e2 * 45.0/1024)
        D = a * e2*e2*e2 * (35.0/3072)
        
        mu = M / A
        
        # 迭代计算足点纬度
        phi1 = mu + B/A * math.sin(2*mu) + C/A * math.sin(4*mu) + D/A * math.sin(6*mu)
        
        # 计算地理坐标
        cos_phi1 = math.cos(phi1)
        sin_phi1 = math.sin(phi1)
        tan_phi1 = math.tan(phi1)
        
        nu1 = a / math.sqrt(1 - e2 * sin_phi1 * sin_phi1)
        rho1 = a * (1 - e2) / math.pow(1 - e2 * sin_phi1 * sin_phi1, 1.5)
        
        eta1_sq = e_prime2 * cos_phi1 * cos_phi1
        
        # 计算经纬度差值
        D_val = x_relative / (nu1 * scale_factor)
        
        # 纬度
        Q1 = nu1 * tan_phi1 / rho1
        Q2 = D_val * D_val / 2.0
        Q3 = (5 + 3*tan_phi1*tan_phi1 + 10*eta1_sq - 4*eta1_sq*eta1_sq - 9*e_prime2) * D_val**4 / 24.0
        Q4 = (61 + 90*tan_phi1*tan_phi1 + 298*eta1_sq + 45*tan_phi1**4 - 1.6*e_prime2 - 37*e_prime2*eta1_sq) * D_val**6 / 720.0
        
        lat = phi1 - Q1 * (Q2 - Q3 + Q4)
        
        # 经度
        Q5 = D_val
        Q6 = (1 + 2*tan_phi1*tan_phi1 + eta1_sq) * D_val**3 / 6.0
        Q7 = (5 - 2*eta1_sq + 28*tan_phi1*tan_phi1 - 3*eta1_sq*eta1_sq + 8*e_prime2 + 24*tan_phi1**4) * D_val**5 / 120.0
        
        delta_lon = (Q5 - Q6 + Q7) / cos_phi1
        lon = math.radians(central_meridian) + delta_lon
        
        # 转换为度
        lat_deg = math.degrees(lat)
        lon_deg = math.degrees(lon)
        
        return lon_deg, lat_deg

# ---------------------------- AVF处理功能 ----------------------------
def split_id_and_x(token: str,
                   max_id_digits: int = 4,
                   ideal_x_int_len: int = 8,
                   min_coord_val: float = 1e5) -> Optional[Tuple[str, str]]:
    """
    把形如 '50437428056.85058594' 拆成 ('504', '37428056.85058594').
    如果无法判定有效切分，返回 None.
    """
    dot = token.find(".")
    if dot == -1:
        return None

    candidates = []
    for k in range(1, min(max_id_digits, dot) + 1):   # k = ID 长度
        id_part, x_part = token[:k], token[k:]

        # x_part 不能以 0 开头（排除 '01234567' 之类）
        if x_part.startswith("0"):
            continue

        try:
            id_val = int(id_part)
            x_val = float(x_part)
        except ValueError:
            continue

        # 坐标必须大于某阈值，避免把小数点前全当成 ID
        if x_val < min_coord_val:
            continue

        # 按 "整数部分长度接近 8 位" 排序
        int_len = len(x_part.split(".")[0])
        candidates.append((abs(int_len - ideal_x_int_len), k, id_val, x_val))

    if not candidates:
        return None

    # 取最优候选
    _, k, id_val, x_val = sorted(candidates)[0]
    return str(id_val), f"{x_val:.8f}"


def fix_avf(path_in: pathlib.Path, path_out: pathlib.Path) -> Tuple[int, int]:
    """
    读取 path_in，修复后写入 path_out。
    返回 (fixed_lines, ambiguous_lines).
    """
    fixed = 0
    ambiguous = 0
    out_lines = []

    with path_in.open("r", encoding="utf-8") as fin:
        for raw in fin:
            line = raw.rstrip("\n")
            fields = line.split()

            # 4 列且首列带小数点 → 可能是 ID+X 粘连
            if len(fields) == 4 and re.fullmatch(r"\d+\.\d+", fields[0]):
                res = split_id_and_x(fields[0])
                if res:
                    id_str, x_str = res
                    new_line = "\t".join([id_str, x_str] + fields[1:]) + "\n"
                    out_lines.append(new_line)
                    fixed += 1
                else:
                    out_lines.append(raw)
                    ambiguous += 1
            else:
                out_lines.append(raw)

    with path_out.open("w", encoding="utf-8") as fout:
        fout.writelines(out_lines)

    return fixed, ambiguous

# ---------------------------- 处理线程 ----------------------------

class ProcessingThread(QThread):
    log_signal = pyqtSignal(str)
    
    def __init__(self, input_file, output_file, x_cell_size, y_cell_size, z_cell_size, 
                 projection_params=None, file_type="point"):
        super().__init__()
        self.input_file = input_file
        self.output_file = output_file
        self.x_cell_size = x_cell_size
        self.y_cell_size = y_cell_size
        self.z_cell_size = z_cell_size
        self.file_type = file_type
        self.projection_params = projection_params or {"type": "gauss_kruger", 
                                                    "degree_code": "3",
                                                    "central_meridian": 105.0}

    def run(self):
        try:
            if self.file_type == "avf_reformat":
                # AVF格式重构
                self.log_signal.emit("使用AVF格式重构模式...")
                self.process_avf_reformat()
            elif self.file_type == "avf":
                # 使用AVF转NetCDF模块
                self.log_signal.emit("使用AVF转NetCDF模式...")
                self.log_signal.emit(f"调用avf_to_nc_simple.convert_avf_to_nc...")
                self.log_signal.emit(f"参数: 输入={self.input_file}, 输出={self.output_file}")
                self.log_signal.emit(f"网格: X={self.x_cell_size}, Y={self.y_cell_size}, Z={self.z_cell_size}")
                self.log_signal.emit(f"投影参数: {self.projection_params}")
                
                avf_to_nc_simple.convert_avf_to_nc(
                    self.input_file, 
                    self.output_file, 
                    self.x_cell_size, 
                    self.y_cell_size, 
                    self.z_cell_size, 
                    self.projection_params
                )
                self.log_signal.emit("AVF转NetCDF处理完成！")
            else:
                # 使用原有的点数据转换
                self.log_signal.emit("使用点数据转NetCDF模式...")
                self.process_point_data()
                
        except Exception as e:
            self.log_signal.emit(f"错误: {str(e)}")
            import traceback
            self.log_signal.emit(traceback.format_exc())

    def process_avf_reformat(self):
        """处理AVF格式重构"""
        self.log_signal.emit(f"正在重构AVF文件: {self.input_file}")
        
        # 生成输出文件名
        if not self.output_file.endswith('.avf') and not self.output_file.endswith('.txt','.dat'):
            base_name = os.path.splitext(self.output_file)[0]
            output_file = f"{base_name}_reformatted.avf"
        else:
            output_file = self.output_file
        
        try:
            fixed, ambiguous = fix_avf(pathlib.Path(self.input_file), pathlib.Path(output_file))
            
            self.log_signal.emit(f"AVF格式重构完成:")
            self.log_signal.emit(f"  成功修复行数: {fixed}")
            self.log_signal.emit(f"  未能判定行数: {ambiguous}")
            self.log_signal.emit(f"  输出文件: {output_file}")
            self.log_signal.emit("重构完成！")
            
        except Exception as e:
            self.log_signal.emit(f"重构失败: {str(e)}")
            raise

    def process_point_data(self):
        """处理点数据（原有的完整版本，包含边界变量）"""
        # 读取点数据 - 增加错误处理
        self.log_signal.emit('正在读取点数据...')
        
        try:
            # 先尝试直接读取
            point_data = np.loadtxt(self.input_file)
        except ValueError as e:
            if "number of columns changed" in str(e):
                self.log_signal.emit('检测到不规则数据格式，尝试逐行解析...')
                # 逐行读取并过滤有效数据
                valid_rows = []
                with open(self.input_file, 'r') as f:
                    for line_num, line in enumerate(f, 1):
                        line = line.strip()
                        if not line or line.startswith('#'):  # 跳过空行和注释行
                            continue
                        
                        parts = line.split()
                        if len(parts) >= 4:  # 至少需要4列数据
                            try:
                                # 尝试转换为数值
                                row = [float(parts[i]) for i in range(4)]
                                valid_rows.append(row)
                            except ValueError:
                                self.log_signal.emit(f'跳过第{line_num}行：无法解析为数值')
                                continue
                        else:
                            self.log_signal.emit(f'跳过第{line_num}行：列数不足({len(parts)}列)')
                
                if not valid_rows:
                    raise ValueError("没有找到有效的数据行")
                
                point_data = np.array(valid_rows)
                self.log_signal.emit(f'成功解析{len(valid_rows)}行有效数据')
            else:
                raise e
        
        x_coords = point_data[:, 0]
        y_coords = point_data[:, 1]
        z_values = point_data[:, 2]
        flag_values = point_data[:, 3]  # 第四列属性值
        
        # 判断是否需要输出为地理坐标
        use_geographic = self.projection_params["type"] == "geographic"
        
        # 保存原始投影坐标，用于网格创建
        proj_x_coords = x_coords.copy()
        proj_y_coords = y_coords.copy()

        # 确定数据范围（使用投影坐标）
        x_min, x_max = np.min(proj_x_coords), np.max(proj_x_coords)
        y_min, y_max = np.min(proj_y_coords), np.max(proj_y_coords)
        z_min, z_max = np.min(z_values), np.max(z_values)

        self.log_signal.emit(f'投影坐标X范围: {x_min} - {x_max}')
        self.log_signal.emit(f'投影坐标Y范围: {y_min} - {y_max}')
        self.log_signal.emit(f'Z范围: {z_min} - {z_max}')
        self.log_signal.emit(f'属性值范围: {np.min(flag_values)} - {np.max(flag_values)}')
        self.log_signal.emit(f'数据点数量: {len(x_coords)}')

        # 计算需要的网格数量 - 基于投影坐标和米为单位的网格大小
        x_grid_count = int(np.ceil((x_max - x_min) / self.x_cell_size)) + 1
        y_grid_count = int(np.ceil((y_max - y_min) / self.y_cell_size)) + 1
        z_grid_count = int(np.ceil((z_max - z_min) / self.z_cell_size)) + 1

        self.log_signal.emit(f'X方向网格数: {x_grid_count}')
        self.log_signal.emit(f'Y方向网格数: {y_grid_count}')
        self.log_signal.emit(f'Z方向网格数: {z_grid_count}')

        # 创建均匀网格 - 在投影坐标系统中创建
        x_padding = self.x_cell_size * 0.5
        y_padding = self.y_cell_size * 0.5
        z_padding = self.z_cell_size * 0.5
        
        # 先在投影坐标系统中创建网格
        proj_x_grid = np.linspace(x_min - x_padding, x_max + x_padding, x_grid_count)
        proj_y_grid = np.linspace(y_min - y_padding, y_max + y_padding, y_grid_count)
        z_grid = np.linspace(z_min - z_padding, z_max + z_padding, z_grid_count)

        # 计算实际网格间距（投影坐标）
        proj_x_spacing = (proj_x_grid[-1] - proj_x_grid[0]) / (x_grid_count - 1)
        proj_y_spacing = (proj_y_grid[-1] - proj_y_grid[0]) / (y_grid_count - 1)
        z_spacing = (z_grid[-1] - z_grid[0]) / (z_grid_count - 1)
        
        self.log_signal.emit(f'X网格实际间距: {proj_x_spacing:.2f} 米')
        self.log_signal.emit(f'Y网格实际间距: {proj_y_spacing:.2f} 米')
        self.log_signal.emit(f'Z网格实际间距: {z_spacing:.2f} 米')
        
        # 如果需要地理坐标，转换网格坐标
        if use_geographic:
            self.log_signal.emit('转换网格到地理坐标系统...')
            converter = CoordinateConverter()
            ellipsoid = self.projection_params.get('ellipsoid', 'cgcs2000')
            
            self.log_signal.emit(f'使用 {ellipsoid.upper()} 椭球参数进行转换...')
            
            # 转换网格点
            x_grid = np.zeros_like(proj_x_grid)
            y_grid = np.zeros_like(proj_y_grid)
            
            # 获取投影带号（使用中间点）
            center_x = (x_min + x_max) / 2
            zone = int(center_x / 1000000)
            self.log_signal.emit(f'检测到投影带号: {zone}')
            
            # 转换X网格（经度）
            for i, x in enumerate(proj_x_grid):
                lon, _ = converter.gauss_kruger_to_geographic(x, proj_y_grid[len(proj_y_grid)//2], zone, ellipsoid)
                x_grid[i] = lon
                
            # 转换Y网格（纬度）
            for i, y in enumerate(proj_y_grid):
                _, lat = converter.gauss_kruger_to_geographic(proj_x_grid[len(proj_x_grid)//2], y, zone, ellipsoid)
                y_grid[i] = lat
                
            x_spacing = (x_grid[-1] - x_grid[0]) / (x_grid_count - 1)
            y_spacing = (y_grid[-1] - y_grid[0]) / (y_grid_count - 1)
            
            self.log_signal.emit(f'经度网格实际间距: {x_spacing:.6f} 度')
            self.log_signal.emit(f'纬度网格实际间距: {y_spacing:.6f} 度')
            self.log_signal.emit(f'转换完成: {x_grid[0]:.6f}°E 到 {x_grid[-1]:.6f}°E, {y_grid[0]:.6f}°N 到 {y_grid[-1]:.6f}°N')
        else:
            x_grid = proj_x_grid
            y_grid = proj_y_grid
            x_spacing = proj_x_spacing
            y_spacing = proj_y_spacing

        # 创建输出的NetCDF文件
        if os.path.exists(self.output_file):
            os.remove(self.output_file)

        self.log_signal.emit(f'创建NetCDF文件: {self.output_file}')
        
        # 确保输出目录存在，避免 HDF5 因目录缺失而报错
        output_path_long = self.output_file
        output_dir = os.path.dirname(output_path_long)
        base_name = os.path.basename(output_path_long)

        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        # 为解决HDF5的中文路径问题，对目录部分使用短路径，文件名保持不变
        short_dir = _get_short_path(output_dir)
        nc_path = os.path.join(short_dir, base_name)

        if os.path.exists(nc_path):
            os.remove(nc_path)

        self.log_signal.emit(f'创建NetCDF文件: {output_path_long}')
        nc = Dataset(nc_path, 'w', format='NETCDF4')

        # 创建维度
        nc.createDimension('x', x_grid_count)
        nc.createDimension('y', y_grid_count)
        nc.createDimension('z', z_grid_count)
        nc.createDimension('nv', 2)

        # 创建坐标变量
        x_var = nc.createVariable('x', 'f8', ('x',))
        y_var = nc.createVariable('y', 'f8', ('y',))
        z_var = nc.createVariable('z', 'f8', ('z',))
        
        # 创建边界变量
        x_bnds = nc.createVariable('x_bnds', 'f8', ('x', 'nv'))
        y_bnds = nc.createVariable('y_bnds', 'f8', ('y', 'nv'))
        z_bnds = nc.createVariable('z_bnds', 'f8', ('z', 'nv'))
        
        # 创建nv坐标变量（解决警告）
        nv_var = nc.createVariable('nv', 'i4', ('nv',))
        nv_var[:] = [0, 1]
        nv_var.long_name = 'vertex number'

        # 创建单元格边界
        x_bounds = np.zeros((x_grid_count, 2))
        y_bounds = np.zeros((y_grid_count, 2))
        z_bounds = np.zeros((z_grid_count, 2))

        for i in range(x_grid_count):
            if i == 0:
                x_bounds[i, 0] = x_grid[i] - x_spacing/2
            else:
                x_bounds[i, 0] = (x_grid[i-1] + x_grid[i]) / 2
                
            if i == x_grid_count-1:
                x_bounds[i, 1] = x_grid[i] + x_spacing/2
            else:
                x_bounds[i, 1] = (x_grid[i] + x_grid[i+1]) / 2

        for i in range(y_grid_count):
            if i == 0:
                y_bounds[i, 0] = y_grid[i] - y_spacing/2
            else:
                y_bounds[i, 0] = (y_grid[i-1] + y_grid[i]) / 2
                
            if i == y_grid_count-1:
                y_bounds[i, 1] = y_grid[i] + y_spacing/2
            else:
                y_bounds[i, 1] = (y_grid[i] + y_grid[i+1]) / 2

        for i in range(z_grid_count):
            if i == 0:
                z_bounds[i, 0] = z_grid[i] - z_spacing/2
            else:
                z_bounds[i, 0] = (z_grid[i-1] + z_grid[i]) / 2
                
            if i == z_grid_count-1:
                z_bounds[i, 1] = z_grid[i] + z_spacing/2
            else:
                z_bounds[i, 1] = (z_grid[i] + z_grid[i+1]) / 2

        # 设置边界值
        x_bnds[:, :] = x_bounds
        y_bnds[:, :] = y_bounds
        z_bnds[:, :] = z_bounds

        # 设置坐标变量属性 - 优化ArcGIS识别
        if use_geographic:
            # 地理坐标系统
            x_var.standard_name = 'longitude'
            x_var.long_name = 'longitude'
            x_var.units = 'degrees_east'
            x_var.actual_range = np.array([x_grid[0], x_grid[-1]])
            x_var.bounds = 'x_bnds'
            x_var.axis = 'X'
            x_var._CoordinateAxisType = 'Lon'

            y_var.standard_name = 'latitude'
            y_var.long_name = 'latitude'
            y_var.units = 'degrees_north'
            y_var.actual_range = np.array([y_grid[0], y_grid[-1]])
            y_var.bounds = 'y_bnds'
            y_var.axis = 'Y'
            y_var._CoordinateAxisType = 'Lat'
        else:
            # 投影坐标系统
            x_var.standard_name = 'projection_x_coordinate'
            x_var.long_name = 'x coordinate of projection'
            x_var.grid_mapping = 'projection'
            x_var.units = 'Meter'
            x_var.actual_range = np.array([x_grid[0], x_grid[-1]])
            x_var.bounds = 'x_bnds'
            x_var.axis = 'X'
            x_var._CoordinateAxisType = 'GeoX'

            y_var.standard_name = 'projection_y_coordinate'
            y_var.long_name = 'y coordinate of projection'
            y_var.grid_mapping = 'projection'
            y_var.units = 'Meter'
            y_var.actual_range = np.array([y_grid[0], y_grid[-1]])
            y_var.bounds = 'y_bnds'
            y_var.axis = 'Y'
            y_var._CoordinateAxisType = 'GeoY'

        z_var.standard_name = 'height'
        z_var.long_name = 'height above reference level'
        z_var.units = 'Meter'
        z_var.actual_range = np.array([z_grid[0], z_grid[-1]])
        z_var.bounds = 'z_bnds'
        z_var.positive = 'up'
        z_var.axis = 'Z'
        z_var._CoordinateAxisType = 'Height'

        # 设置坐标值
        x_var[:] = x_grid
        y_var[:] = y_grid
        z_var[:] = z_grid

        # 创建投影变量（作为标量变量以避免警告）
        if not use_geographic:
            projection = nc.createVariable('projection', 'i4', ())
            projection.grid_mapping_name = "transverse_mercator"
        
        # 创建数据变量 - 优化渲染属性
        fill_value = -9999.0  # 使用更标准的填充值
        oxygen = nc.createVariable('Oxygen_Prediction', 'f4', ('z', 'y', 'x'), 
                                 fill_value=fill_value, chunksizes=None, zlib=True, complevel=1)
        
        # 设置数据变量属性 - 优化ArcGIS渲染
        oxygen.standard_name = 'oxygen_prediction'
        oxygen.long_name = 'Oxygen Prediction from GOCAD Model'
        if not use_geographic:
            oxygen.grid_mapping = 'projection'
        oxygen.coordinates = 'x y z'
        
        # 根据投影参数设置esri_pe_string
        if use_geographic:
            # 地理坐标系 - CGCS2000/EPSG:4490
            esri_pe_string = 'GEOGCS["GCS_China_Geodetic_Coordinate_System_2000",DATUM["D_China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],VERTCS["Unknown VCS from ArcInfo Workstation",VDATUM["Unknown"],PARAMETER["Vertical_Shift",0.0],PARAMETER["Direction",1.0],UNIT["Meter",1.0]]'
        else:
            # 高斯克吕格投影
            degree_code = self.projection_params["degree_code"]
            central_meridian = self.projection_params["central_meridian"]
            if degree_code == "3":
                zone = int((central_meridian + 1.5) / 3)
                esri_pe_string = f'PROJCS["CGCS2000_3_Degree_GK_Zone_{zone}",GEOGCS["GCS_China_Geodetic_Coordinate_System_2000",DATUM["D_China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Gauss_Kruger"],PARAMETER["False_Easting",500000.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",{central_meridian}],PARAMETER["Scale_Factor",1.0],PARAMETER["Latitude_Of_Origin",0.0],UNIT["Meter",1.0]],VERTCS["Unknown VCS from ArcInfo Workstation",VDATUM["Unknown"],PARAMETER["Vertical_Shift",0.0],PARAMETER["Direction",1.0],UNIT["Meter",1.0]]'
            else:
                zone = int(central_meridian / 6) + 1
                esri_pe_string = f'PROJCS["CGCS2000_3_Degree_GK_CM_{int(central_meridian)}E",GEOGCS["GCS_China_Geodetic_Coordinate_System_2000",DATUM["D_China_2000",SPHEROID["CGCS2000",6378137.0,298.257222101]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Gauss_Kruger"],PARAMETER["False_Easting",500000.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",{central_meridian}],PARAMETER["Scale_Factor",1.0],PARAMETER["Latitude_Of_Origin",0.0],UNIT["Meter",1.0]],VERTCS["Unknown VCS from ArcInfo Workstation",VDATUM["Unknown"],PARAMETER["Vertical_Shift",0.0],PARAMETER["Direction",1.0],UNIT["Meter",1.0]]'
        
        # 优化ArcGIS渲染的属性设置
        oxygen.esri_pe_string = esri_pe_string
        oxygen.valid_range = np.array([np.min(flag_values), np.max(flag_values)], dtype=np.float32)
        oxygen.scale_factor = 1.0
        oxygen.add_offset = 0.0
        
        # ArcGIS渲染优化属性
        oxygen.cell_methods = "area: mean"
        oxygen.display_type = "surface"
        oxygen.rendering_method = "continuous"
        oxygen.interpolation_method = "nearest_neighbor"  # 明确表示有插值
        
        self.log_signal.emit('正在使用最近邻插值将点云数据格网化...')
        
        # 准备插值点 (使用投影坐标)
        points = np.c_[proj_x_coords, proj_y_coords, z_values]
        values = flag_values

        # 初始化输出网格
        grid_data = np.full((z_grid_count, y_grid_count, x_grid_count), fill_value, dtype=np.float32)

        self.log_signal.emit('为提高效率，将进行分块处理...')
        
        try:
            # 1. 在原始数据点上构建 cKDTree (此步骤很快)
            tree = cKDTree(points)
            self.log_signal.emit('已在原始数据点上构建KD树用于快速查询。')
            
            # 2. 定义距离阈值，用于后续的掩码操作
            dist_threshold = np.sqrt(self.x_cell_size**2 + self.y_cell_size**2 + self.z_cell_size**2)
            self.log_signal.emit(f'距离阈值设置为: {dist_threshold:.2f} 米')

            # 3. 按 Z 切片高效迭代处理
            total_masked_points = 0
            for k in range(z_grid_count):
                # 仅在关键节点打印日志，避免刷屏
                if k == 0 or (k + 1) % 10 == 0 or k == z_grid_count - 1:
                    self.log_signal.emit(f'正在处理 Z 切片 {k + 1}/{z_grid_count}...')
                
                # 为当前切片创建查询网格点 (高效的向量化操作)
                g_y_slice, g_x_slice = np.meshgrid(proj_y_grid, proj_x_grid, indexing='ij')
                z_k_array = np.full_like(g_x_slice, z_grid[k])
                query_points_slice = np.c_[g_x_slice.ravel(), g_y_slice.ravel(), z_k_array.ravel()]

                # a. 对当前切片进行插值
                slice_data = griddata(points, values, query_points_slice, method='nearest')
                
                # b. 使用KDTree查询距离以创建掩码
                distances, _ = tree.query(query_points_slice, k=1)
                
                # c. 将距离过远的格网点设置回填充值
                mask = distances > dist_threshold
                slice_data[mask] = fill_value
                total_masked_points += np.sum(mask)

                # d. 将处理好的切片数据放回总网格
                grid_data[k, :, :] = slice_data.reshape(y_grid_count, x_grid_count)
            
            self.log_signal.emit(f'Z向切片处理完成，共 {z_grid_count} 个切片。')
            self.log_signal.emit(f'共移除了 {total_masked_points} 个数据范围外的插值点。')

        except Exception as e:
            self.log_signal.emit(f'插值或掩码处理失败: {e}')
            self.log_signal.emit('将创建一个空的网格。')
            grid_data = np.full((z_grid_count, y_grid_count, x_grid_count), fill_value, dtype=np.float32)
        
        grid_data = grid_data.astype(np.float32)

        # 统计有效数据点
        valid_count = np.sum(grid_data != fill_value)
        self.log_signal.emit(f'有效网格点数量: {valid_count} (总网格点: {x_grid_count * y_grid_count * z_grid_count})')
        self.log_signal.emit(f'数据覆盖率: {valid_count / (x_grid_count * y_grid_count * z_grid_count) * 100:.2f}%')

        # 将数据写入NetCDF变量
        self.log_signal.emit('写入NetCDF文件...')
        oxygen[:, :, :] = grid_data

        # 设置投影属性
        if not use_geographic:
            projection.longitude_of_central_meridian = self.projection_params["central_meridian"]
            projection.latitude_of_projection_origin = 0.0
            projection.scale_factor_at_central_meridian = 1.0
            projection.false_easting = 500000.0
            projection.false_northing = 0.0
            projection.spatial_ref = esri_pe_string
            projection.GeoTransform = f"{x_grid[0]} {x_spacing} 0.0 {y_grid[-1]} 0.0 {-y_spacing}"

        # 设置全局属性 - 优化ArcGIS识别
        nc.Conventions = 'CF-1.6'
        nc.title = "GOCAD Point Cloud Data"
        nc.source = "GOCAD model exported as point cloud, no interpolation applied"
        nc.history = f"Created from GOCAD point cloud on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        if use_geographic:
            nc.references = "CGCS2000 Geographic Coordinate System (EPSG:4490)"
            # 地理坐标系统的GeoTransform
            nc.GeoTransform = f"{x_grid[0]} {x_spacing} 0.0 {y_grid[-1]} 0.0 {-y_spacing}"
        else:
            if self.projection_params["degree_code"] == "3":
                zone = int((self.projection_params["central_meridian"] + 1.5) / 3)
                nc.references = f"CGCS2000_3_Degree_GK_Zone_{zone}"
            else:
                nc.references = f"CGCS2000_3_Degree_GK_CM_{int(self.projection_params['central_meridian'])}E"
        
        nc.comment = "Original GOCAD point data interpolated to a regular grid using nearest neighbor method."
        nc.institution = "GOCAD Data Processing"
        nc.Source_Software = 'Python NetCDF4'
        nc.spatial_ref = esri_pe_string
        nc.data_preservation = "interpolated"
        nc.interpolation_applied = "nearest_neighbor"
        
        # 完成并关闭文件
        nc.close()
        self.log_signal.emit(f'NetCDF文件已创建: {self.output_file}')
        self.log_signal.emit('处理完成！原始点云数据已精确保存，有插值处理。')

# ---------------------------- 主界面 ----------------------------

class PointToNCApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("数据转NetCDF工具 (支持点数据、AVF数据和AVF重构)")
        self.setGeometry(100, 100, 750, 800)
        
        # 初始化变量
        self.input_file = ""
        self.output_file = ""
        
        self.setup_ui()
    
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 文件类型选择
        type_group = QGroupBox("数据类型选择")
        type_layout = QVBoxLayout(type_group)
        
        type_row = QHBoxLayout()
        type_row.addWidget(QLabel("数据类型:"))
        self.file_type_combo = QComboBox()
        self.file_type_combo.addItems(['point', 'avf', 'avf_reformat'])
        self.file_type_combo.currentTextChanged.connect(self.on_file_type_change)
        type_row.addWidget(self.file_type_combo)
        type_row.addStretch()
        type_layout.addLayout(type_row)
        
        type_layout.addWidget(QLabel("点数据: 4列格式(X Y Z VALUE)"))
        type_layout.addWidget(QLabel("AVF数据: GOCAD AVF格式转NetCDF"))
        type_layout.addWidget(QLabel("AVF重构: 修复AVF格式问题"))
        
        main_layout.addWidget(type_group)
        
        # 文件选择部分
        file_group = QGroupBox("文件设置")
        file_layout = QGridLayout(file_group)
        
        # 输入文件
        file_layout.addWidget(QLabel("输入文件:"), 0, 0)
        self.input_file_edit = QLineEdit()
        file_layout.addWidget(self.input_file_edit, 0, 1)
        input_browse_btn = QPushButton("浏览")
        input_browse_btn.clicked.connect(self.browse_input)
        file_layout.addWidget(input_browse_btn, 0, 2)
        
        # 输出文件
        file_layout.addWidget(QLabel("输出文件:"), 1, 0)
        self.output_file_edit = QLineEdit()
        file_layout.addWidget(self.output_file_edit, 1, 1)
        output_browse_btn = QPushButton("浏览")
        output_browse_btn.clicked.connect(self.browse_output)
        file_layout.addWidget(output_browse_btn, 1, 2)
        
        main_layout.addWidget(file_group)
        
        # 网格参数部分
        self.param_group = QGroupBox("网格参数设置")
        param_layout = QGridLayout(self.param_group)
        
        # X方向格子大小
        self.x_label = QLabel("X方向格子大小 (米):")
        param_layout.addWidget(self.x_label, 0, 0)
        self.x_cell_size_spin = QDoubleSpinBox()
        self.x_cell_size_spin.setRange(0.01, 10000)
        self.x_cell_size_spin.setValue(55.0)
        self.x_cell_size_spin.setDecimals(2)
        param_layout.addWidget(self.x_cell_size_spin, 0, 1)
        
        # Y方向格子大小
        self.y_label = QLabel("Y方向格子大小 (米):")
        param_layout.addWidget(self.y_label, 1, 0)
        self.y_cell_size_spin = QDoubleSpinBox()
        self.y_cell_size_spin.setRange(0.01, 10000)
        self.y_cell_size_spin.setValue(55.0)
        self.y_cell_size_spin.setDecimals(2)
        param_layout.addWidget(self.y_cell_size_spin, 1, 1)
        
        # Z方向格子大小
        param_layout.addWidget(QLabel("Z方向格子大小 (米):"), 2, 0)
        self.z_cell_size_spin = QDoubleSpinBox()
        self.z_cell_size_spin.setRange(0.01, 1000)
        self.z_cell_size_spin.setValue(0.6)
        self.z_cell_size_spin.setDecimals(2)
        param_layout.addWidget(self.z_cell_size_spin, 2, 1)
        
        main_layout.addWidget(self.param_group)
        
        # 投影坐标系统设置
        self.proj_group = QGroupBox("投影坐标系统设置")
        proj_layout = QVBoxLayout(self.proj_group)
        
        # 投影类型选择
        proj_type_row = QHBoxLayout()
        proj_type_row.addWidget(QLabel("投影类型:"))
        self.projection_type_combo = QComboBox()
        self.projection_type_combo.addItems(['gauss_kruger', 'geographic'])
        self.projection_type_combo.currentTextChanged.connect(self.on_projection_change)
        proj_type_row.addWidget(self.projection_type_combo)
        proj_type_row.addStretch()
        proj_layout.addLayout(proj_type_row)
        
        # 高斯克吕格投影参数
        self.gauss_widget = QWidget()
        gauss_layout = QGridLayout(self.gauss_widget)
        
        gauss_layout.addWidget(QLabel("度带代号:"), 0, 0)
        self.degree_combo = QComboBox()
        self.degree_combo.addItems(['3', '6'])
        self.degree_combo.currentTextChanged.connect(self.on_degree_change)
        gauss_layout.addWidget(self.degree_combo, 0, 1)
        
        gauss_layout.addWidget(QLabel("中央子午线:"), 1, 0)
        self.central_meridian_spin = QDoubleSpinBox()
        self.central_meridian_spin.setRange(-180, 180)
        self.central_meridian_spin.setValue(102.0)
        self.central_meridian_spin.setDecimals(1)
        gauss_layout.addWidget(self.central_meridian_spin, 1, 1)
        
        proj_layout.addWidget(self.gauss_widget)
        
        # 源坐标系统选择（仅在地理坐标输出时显示）
        self.source_crs_widget = QWidget()
        source_crs_layout = QGridLayout(self.source_crs_widget)
        
        source_crs_layout.addWidget(QLabel("输入数据的源坐标系统:"), 0, 0)
        self.source_crs_combo = QComboBox()
        self.source_crs_combo.addItems([
            "CGCS2000坐标系 (推荐)",
            "西安80坐标系", 
            "北京54坐标系"
        ])
        source_crs_layout.addWidget(self.source_crs_combo, 0, 1)
        
        # 添加说明文字
        source_crs_info = QLabel("注意：必须选择与输入数据匹配的源坐标系统，否则转换结果会有误差")
        source_crs_info.setStyleSheet("color: red; font-size: 10px;")
        source_crs_info.setWordWrap(True)
        source_crs_layout.addWidget(source_crs_info, 1, 0, 1, 2)
        
        proj_layout.addWidget(self.source_crs_widget)
        self.source_crs_widget.hide()  # 初始隐藏
        
        main_layout.addWidget(self.proj_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        start_btn = QPushButton("开始处理")
        start_btn.clicked.connect(self.start_process)
        button_layout.addWidget(start_btn)
        button_layout.addStretch()
        exit_btn = QPushButton("退出")
        exit_btn.clicked.connect(self.close)
        button_layout.addWidget(exit_btn)
        
        main_layout.addLayout(button_layout)
        
        # 日志输出区域
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        main_layout.addWidget(log_group)
    
    def on_projection_change(self):
        """投影类型变化时的处理"""
        if self.projection_type_combo.currentText() == "gauss_kruger":
            self.gauss_widget.show()
            self.source_crs_widget.hide()
            # 投影坐标系统时，网格单位为米
            self.param_group.setTitle("网格参数设置")
            self.x_label.setText("X方向格子大小 (米):")
            self.y_label.setText("Y方向格子大小 (米):")
        else:  # geographic
            self.gauss_widget.hide()
            self.source_crs_widget.show()
            # 地理坐标系统输出，但网格参数仍然用米设置
            self.param_group.setTitle("网格参数设置 (将输出为地理坐标)")
            self.x_label.setText("X方向格子大小 (米):")
            self.y_label.setText("Y方向格子大小 (米):")
    
    def on_file_type_change(self):
        """文件类型改变时的处理"""
        file_type = self.file_type_combo.currentText()
        
        if file_type == "avf_reformat":
            # AVF重构模式：隐藏网格参数和投影设置
            self.param_group.hide()
            self.proj_group.hide()
        elif file_type == "avf":
            # AVF转NetCDF模式：显示网格参数和投影设置
            self.param_group.show()
            self.proj_group.show()
            # AVF默认使用高斯投影
            if self.projection_type_combo.currentText() != "gauss_kruger":
                self.projection_type_combo.setCurrentText("gauss_kruger")
                self.on_projection_change()
        else:  # point
            # 点数据模式：显示网格参数和投影设置
            self.param_group.show()
            self.proj_group.show()
    
    def on_degree_change(self):
        # 根据度带代号自动设置中央子午线
        degree = self.degree_combo.currentText()
        if degree == "3":
            if int(self.central_meridian_spin.value()) != 102:
                self.central_meridian_spin.setValue(102.0)
        elif degree == "6":
            if int(self.central_meridian_spin.value()) != 105:
                self.central_meridian_spin.setValue(105.0)
    
    def browse_input(self):
        file_type = self.file_type_combo.currentText()
        if file_type in ["avf", "avf_reformat"]:
            file_filter = "AVF文件 (*.avf);;文本文件 (*.txt);;所有文件 (*.*)"
        else:
            file_filter = "文本文件 (*.txt);;所有文件 (*.*)"
            
        filename, _ = QFileDialog.getOpenFileName(
            self, "选择输入文件", "", file_filter
        )
        if filename:
            self.input_file = filename
            self.input_file_edit.setText(filename)
            # 如果没有设置输出文件，自动生成一个输出文件名
            if not self.output_file_edit.text():
                base = os.path.splitext(filename)[0]
                if file_type == "avf_reformat":
                    self.output_file = base + "_reformatted.avf"
                else:
                    self.output_file = base + ".nc"
                self.output_file_edit.setText(self.output_file)
    
    def browse_output(self):
        file_type = self.file_type_combo.currentText()
        if file_type == "avf_reformat":
            file_filter = "AVF文件 (*.avf);;文本文件 (*.txt);;所有文件 (*.*)"
            default_ext = ".avf"
        else:
            file_filter = "NetCDF文件 (*.nc);;所有文件 (*.*)"
            default_ext = ".nc"
            
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存输出文件", "", file_filter
        )
        if filename:
            self.output_file = filename
            self.output_file_edit.setText(filename)
    
    def start_process(self):
        # 获取参数
        input_file = self.input_file_edit.text()
        output_file = self.output_file_edit.text()
        file_type = self.file_type_combo.currentText()
        
        # 参数验证
        if not input_file or not output_file:
            QMessageBox.warning(self, "错误", "请指定输入和输出文件！")
            return
        
        # 对于非AVF重构模式，需要检查网格参数
        if file_type != "avf_reformat":
            x_size = self.x_cell_size_spin.value()
            y_size = self.y_cell_size_spin.value()
            z_size = self.z_cell_size_spin.value()
            
            if x_size <= 0 or y_size <= 0 or z_size <= 0:
                QMessageBox.warning(self, "错误", "格子大小必须大于0！")
                return
        else:
            x_size = y_size = z_size = 1.0  # 占位值
        
        # 清空日志
        self.log_text.clear()
        
        # 显示开始信息
        if file_type == "avf_reformat":
            self.show_log("开始处理 (AVF格式重构)...")
        elif file_type == "avf":
            self.show_log("开始处理 (AVF转NetCDF)...")
        else:
            self.show_log("开始处理 (点数据转NetCDF)...")
        
        self.show_log(f"输入文件: {input_file}")
        self.show_log(f"输出文件: {output_file}")
        
        if file_type != "avf_reformat":
            self.show_log(f"X方向格子大小: {x_size} 米")
            self.show_log(f"Y方向格子大小: {y_size} 米")
            self.show_log(f"Z方向格子大小: {z_size} 米")
        
        # 设置投影参数（仅对非AVF重构模式）
        projection_params = {}
        if file_type != "avf_reformat":
            projection_params["type"] = self.projection_type_combo.currentText()
            
            if projection_params["type"] == "gauss_kruger":
                projection_params["degree_code"] = self.degree_combo.currentText()
                projection_params["central_meridian"] = self.central_meridian_spin.value()
                self.show_log(f"投影类型: 高斯克吕格 (CGCS2000)")
                self.show_log(f"度带代号: {projection_params['degree_code']}")
                self.show_log(f"中央子午线: {projection_params['central_meridian']}")
            else:  # geographic
                # 获取源坐标系统
                source_crs_mapping = {
                    0: "cgcs2000",
                    1: "xian80", 
                    2: "beijing54"
                }
                source_ellipsoid = source_crs_mapping[self.source_crs_combo.currentIndex()]
                projection_params["ellipsoid"] = source_ellipsoid
                
                source_crs_names = {
                    "cgcs2000": "CGCS2000坐标系",
                    "xian80": "西安80坐标系",
                    "beijing54": "北京54坐标系"
                }
                
                self.show_log(f"投影类型: 地理坐标系统 (CGCS2000/EPSG:4490)")
                self.show_log(f"输入数据源坐标系统: {source_crs_names[source_ellipsoid]}")
                self.show_log(f"椭球体: {source_ellipsoid.upper()}")
                self.show_log(f"注：网格在投影坐标系统中创建，然后转换为地理坐标")
        
        self.show_log("")
        
        # 创建处理线程
        self.processing_thread = ProcessingThread(
            input_file, output_file, x_size, y_size, z_size, 
            projection_params, file_type
        )
        self.processing_thread.log_signal.connect(self.show_log)
        self.processing_thread.start()
    
    def show_log(self, message):
        self.log_text.append(message)
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)

def _get_short_path(path_str: str) -> str:
    """在 Windows 下将路径转换为 8.3 格式，解决 HDF5 对中文路径的兼容性问题。"""
    if platform.system() != 'Windows' or not path_str:
        return path_str
    try:
        GetShortPathNameW = ctypes.windll.kernel32.GetShortPathNameW
        GetShortPathNameW.argtypes = [ctypes.c_wchar_p, ctypes.c_wchar_p, ctypes.c_uint]
        GetShortPathNameW.restype = ctypes.c_uint
        buffer = ctypes.create_unicode_buffer(260)
        result = GetShortPathNameW(path_str, buffer, 260)
        if result == 0:
            return path_str  # 失败则返回原路径
        return buffer.value
    except Exception:
        return path_str

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = PointToNCApp()
    window.show()
    sys.exit(app.exec_())